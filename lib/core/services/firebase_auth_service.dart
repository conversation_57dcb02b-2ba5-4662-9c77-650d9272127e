import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter/foundation.dart';
import 'package:dartz/dartz.dart';

import '../error/network_exceptions.dart';
import '../utils/log_service.dart';
import '../../features/auth/domain/entities/user.dart' as domain;

/// Firebase认证服务
class FirebaseAuthService {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  final LogService _logger;

  FirebaseAuthService({
    firebase_auth.FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
    LogService? logger,
  })  : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn(),
        _logger = logger ?? LogService();

  /// 获取当前用户
  domain.User? get currentUser {
    final firebaseUser = _firebaseAuth.currentUser;
    if (firebaseUser == null) return null;
    
    return _mapFirebaseUserToDomainUser(firebaseUser);
  }

  /// 用户状态流
  Stream<domain.User?> get userChanges {
    return _firebaseAuth.userChanges().map((firebaseUser) {
      if (firebaseUser == null) return null;
      return _mapFirebaseUserToDomainUser(firebaseUser);
    });
  }

  /// 邮箱密码注册
  Future<Either<NetworkExceptions, domain.User>> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? name,
  }) async {
    try {
      _logger.i('开始邮箱密码注册: $email');
      
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(NetworkExceptions.unexpectedError());
      }

      // 更新用户显示名称
      if (name != null && name.isNotEmpty) {
        await credential.user!.updateDisplayName(name);
        await credential.user!.reload();
      }

      // 发送邮箱验证
      if (!credential.user!.emailVerified) {
        await credential.user!.sendEmailVerification();
      }

      final user = _mapFirebaseUserToDomainUser(credential.user!);
      _logger.i('邮箱密码注册成功: ${user.id}');
      
      return Right(user);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('邮箱密码注册失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('邮箱密码注册异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 邮箱密码登录
  Future<Either<NetworkExceptions, domain.User>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _logger.i('开始邮箱密码登录: $email');
      
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user == null) {
        return const Left(NetworkExceptions.unexpectedError());
      }

      final user = _mapFirebaseUserToDomainUser(credential.user!);
      _logger.i('邮箱密码登录成功: ${user.id}');
      
      return Right(user);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('邮箱密码登录失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('邮箱密码登录异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// Google登录
  Future<Either<NetworkExceptions, domain.User>> signInWithGoogle() async {
    try {
      _logger.i('开始Google登录');
      
      // 触发Google登录流程
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        return const Left(NetworkExceptions.requestCancelled());
      }

      // 获取认证详情
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // 创建Firebase凭证
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // 使用凭证登录Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      if (userCredential.user == null) {
        return const Left(NetworkExceptions.unexpectedError());
      }

      final user = _mapFirebaseUserToDomainUser(userCredential.user!);
      _logger.i('Google登录成功: ${user.id}');
      
      return Right(user);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('Google登录失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('Google登录异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// Apple登录
  Future<Either<NetworkExceptions, domain.User>> signInWithApple() async {
    try {
      _logger.i('开始Apple登录');
      
      // 检查Apple登录可用性
      if (!await SignInWithApple.isAvailable()) {
        return const Left(NetworkExceptions.defaultError('Apple登录不可用'));
      }

      // 触发Apple登录流程
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // 创建Firebase凭证
      final oauthCredential = firebase_auth.OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // 使用凭证登录Firebase
      final userCredential = await _firebaseAuth.signInWithCredential(oauthCredential);
      
      if (userCredential.user == null) {
        return const Left(NetworkExceptions.unexpectedError());
      }

      // 如果是新用户且有姓名信息，更新显示名称
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        final fullName = appleCredential.givenName != null && appleCredential.familyName != null
            ? '${appleCredential.givenName} ${appleCredential.familyName}'
            : null;
        
        if (fullName != null && fullName.isNotEmpty) {
          await userCredential.user!.updateDisplayName(fullName);
          await userCredential.user!.reload();
        }
      }

      final user = _mapFirebaseUserToDomainUser(userCredential.user!);
      _logger.i('Apple登录成功: ${user.id}');
      
      return Right(user);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('Apple登录失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('Apple登录异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 发送密码重置邮件
  Future<Either<NetworkExceptions, void>> sendPasswordResetEmail({
    required String email,
  }) async {
    try {
      _logger.i('发送密码重置邮件: $email');
      
      await _firebaseAuth.sendPasswordResetEmail(email: email);
      
      _logger.i('密码重置邮件发送成功');
      return const Right(null);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('发送密码重置邮件失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('发送密码重置邮件异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 发送邮箱验证
  Future<Either<NetworkExceptions, void>> sendEmailVerification() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(NetworkExceptions.unauthorizedRequest());
      }

      _logger.i('发送邮箱验证: ${user.email}');
      
      await user.sendEmailVerification();
      
      _logger.i('邮箱验证发送成功');
      return const Right(null);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('发送邮箱验证失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('发送邮箱验证异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 登出
  Future<Either<NetworkExceptions, void>> signOut() async {
    try {
      _logger.i('开始登出');
      
      // 同时登出Firebase和Google
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);
      
      _logger.i('登出成功');
      return const Right(null);
    } catch (e) {
      _logger.e('登出异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 删除账户
  Future<Either<NetworkExceptions, void>> deleteAccount() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return const Left(NetworkExceptions.unauthorizedRequest());
      }

      _logger.i('删除账户: ${user.uid}');
      
      await user.delete();
      
      _logger.i('账户删除成功');
      return const Right(null);
    } on firebase_auth.FirebaseAuthException catch (e) {
      _logger.e('删除账户失败', e);
      return Left(_mapFirebaseAuthException(e));
    } catch (e) {
      _logger.e('删除账户异常', e);
      return const Left(NetworkExceptions.unexpectedError());
    }
  }

  /// 将Firebase用户映射为领域用户
  domain.User _mapFirebaseUserToDomainUser(firebase_auth.User firebaseUser) {
    return domain.User(
      id: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      name: firebaseUser.displayName,
      avatar: firebaseUser.photoURL,
      phoneNumber: firebaseUser.phoneNumber,
      isEmailVerified: firebaseUser.emailVerified,
      isPremiumUser: false, // 默认为普通用户，需要从后端获取
      dailyGenerationCount: 0,
      monthlyGenerationCount: 0,
      createdAt: firebaseUser.metadata.creationTime,
      updatedAt: DateTime.now(),
      lastLoginAt: firebaseUser.metadata.lastSignInTime,
    );
  }

  /// 映射Firebase认证异常
  NetworkExceptions _mapFirebaseAuthException(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return const NetworkExceptions.notFound('用户不存在');
      case 'wrong-password':
        return const NetworkExceptions.unauthorizedRequest();
      case 'email-already-in-use':
        return const NetworkExceptions.defaultError('邮箱已被使用');
      case 'weak-password':
        return const NetworkExceptions.badRequest();
      case 'invalid-email':
        return const NetworkExceptions.badRequest();
      case 'user-disabled':
        return const NetworkExceptions.forbidden();
      case 'too-many-requests':
        return const NetworkExceptions.requestTimeout();
      case 'operation-not-allowed':
        return const NetworkExceptions.forbidden();
      case 'requires-recent-login':
        return const NetworkExceptions.unauthorizedRequest();
      default:
        return NetworkExceptions.defaultError(e.message ?? '认证失败');
    }
  }
}
