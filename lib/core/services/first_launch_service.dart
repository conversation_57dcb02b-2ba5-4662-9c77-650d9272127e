import 'package:shared_preferences/shared_preferences.dart';

/// 首次启动服务
/// 用于管理应用的首次启动状态和引导流程
class FirstLaunchService {
  static const String _keyFirstLaunch = 'first_launch';
  static const String _keyOnboardingCompleted = 'onboarding_completed';
  
  final SharedPreferences _prefs;
  
  FirstLaunchService(this._prefs);
  
  /// 检查是否为首次启动
  bool get isFirstLaunch {
    return _prefs.getBool(_keyFirstLaunch) ?? true;
  }
  
  /// 检查引导流程是否已完成
  bool get isOnboardingCompleted {
    return _prefs.getBool(_keyOnboardingCompleted) ?? false;
  }
  
  /// 标记首次启动已完成
  Future<void> markFirstLaunchCompleted() async {
    await _prefs.setBool(_keyFirstLaunch, false);
  }
  
  /// 标记引导流程已完成
  Future<void> markOnboardingCompleted() async {
    await _prefs.setBool(_keyOnboardingCompleted, true);
  }
  
  /// 重置首次启动状态（用于测试）
  Future<void> resetFirstLaunch() async {
    await _prefs.remove(_keyFirstLaunch);
    await _prefs.remove(_keyOnboardingCompleted);
  }
  
  /// 获取应用启动后应该导航到的路由
  String getInitialRoute() {
    if (isFirstLaunch || !isOnboardingCompleted) {
      return '/onboarding';
    }
    
    // TODO: 检查用户登录状态
    // if (!isLoggedIn) {
    //   return '/auth';
    // }
    
    return '/main';
  }
}
