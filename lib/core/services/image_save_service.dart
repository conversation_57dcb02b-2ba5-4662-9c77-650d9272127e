import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

import '../utils/log_service.dart';

/// 图片保存服务（简化版）
///
/// 直接保存图片到相册，让系统自动处理权限请求
/// 类似iOS的UIImageWriteToSavedPhotosAlbum功能
class ImageSaveService {
  static final Dio _dio = Dio();
  static final LogService _logger = LogService();

  /// 直接保存网络图片到相册（简化版）
  static Future<bool> saveNetworkImageToGallery(
    String imageUrl, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      _logger.i('开始保存网络图片到相册: $imageUrl');

      // 下载图片
      final response = await _dio.get(
        imageUrl,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final Uint8List imageBytes = Uint8List.fromList(response.data);

        // 直接保存到相册，让系统处理权限
        final result = await ImageGallerySaver.saveImage(
          imageBytes,
          name: fileName ?? 'hera_wedding_photo_${DateTime.now().millisecondsSinceEpoch}',
          quality: 100,
        );

        if (result['isSuccess'] == true) {
          if (context != null && context.mounted) {
            _showSuccessMessage(context);
          }
          _logger.i('图片保存成功');
          return true;
        } else {
          final errorMsg = result['errorMessage'] ?? '未知错误';
          _logger.w('图片保存失败: $errorMsg');
          if (context != null && context.mounted) {
            _showErrorMessage(context, '保存失败：$errorMsg');
          }
          return false;
        }
      } else {
        _logger.w('下载图片失败: ${response.statusCode}');
        if (context != null && context.mounted) {
          _showErrorMessage(context, '下载图片失败');
        }
        return false;
      }
    } catch (e) {
      _logger.e('保存图片异常', e);
      if (context != null && context.mounted) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 直接保存本地图片到相册（简化版）
  static Future<bool> saveLocalImageToGallery(
    File imageFile, {
    BuildContext? context,
    String? fileName,
  }) async {
    try {
      _logger.i('开始保存本地图片到相册: ${imageFile.path}');

      // 读取图片数据
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // 直接保存到相册，让系统处理权限
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: fileName ?? 'hera_photo_${DateTime.now().millisecondsSinceEpoch}',
        quality: 100,
      );

      if (result['isSuccess'] == true) {
        if (context != null && context.mounted) {
          _showSuccessMessage(context);
        }
        _logger.i('本地图片保存成功');
        return true;
      } else {
        final errorMsg = result['errorMessage'] ?? '未知错误';
        _logger.w('本地图片保存失败: $errorMsg');
        if (context != null && context.mounted) {
          _showErrorMessage(context, '保存失败：$errorMsg');
        }
        return false;
      }
    } catch (e) {
      _logger.e('保存本地图片异常', e);
      if (context != null && context.mounted) {
        _showErrorMessage(context, '保存失败：$e');
      }
      return false;
    }
  }

  /// 批量保存图片到相册（简化版）
  static Future<Map<String, int>> saveBatchImages({
    List<String>? networkUrls,
    List<File>? localFiles,
    BuildContext? context,
    Function(int current, int total)? onProgress,
  }) async {
    int successCount = 0;
    int failureCount = 0;
    int totalCount = (networkUrls?.length ?? 0) + (localFiles?.length ?? 0);

    if (totalCount == 0) {
      return {'success': 0, 'failure': 0};
    }

    _logger.i('开始批量保存图片，总数: $totalCount');
    int currentIndex = 0;

    // 保存网络图片
    if (networkUrls != null) {
      for (final url in networkUrls) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);

        final success = await saveNetworkImageToGallery(url);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 保存本地图片
    if (localFiles != null) {
      for (final file in localFiles) {
        currentIndex++;
        onProgress?.call(currentIndex, totalCount);

        final success = await saveLocalImageToGallery(file);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      }
    }

    // 显示批量保存结果
    if (context != null && context.mounted) {
      _showBatchSaveResult(context, successCount, failureCount);
    }

    _logger.i('批量保存完成: 成功$successCount张，失败$failureCount张');
    return {'success': successCount, 'failure': failureCount};
  }



  /// 显示成功消息
  static void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            const Text('图片已保存到相册'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误消息
  static void _showErrorMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示批量保存结果
  static void _showBatchSaveResult(BuildContext context, int successCount, int failureCount) {
    final totalCount = successCount + failureCount;
    final isAllSuccess = failureCount == 0;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isAllSuccess ? Icons.check_circle : Icons.warning,
              color: Colors.white,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                isAllSuccess
                    ? '所有图片保存成功！($totalCount张)'
                    : '保存完成：成功$successCount张，失败$failureCount张',
              ),
            ),
          ],
        ),
        backgroundColor: isAllSuccess ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }


}