import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../providers/app_providers.dart';
import 'wedding_colors.dart';
import 'wedding_typography.dart';

/// 婚礼主题配置
class AppTheme {
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    colorScheme: WeddingColors.lightColorScheme,
    textTheme: WeddingTypography.lightTextTheme,
    scaffoldBackgroundColor: WeddingColors.lightGray,

    // AppBar主题
    appBarTheme: AppBarTheme(
      centerTitle: true,
      elevation: 0,
      backgroundColor: WeddingColors.pureWhite,
      surfaceTintColor: Colors.transparent,
      shadowColor: WeddingColors.shadowLight,
      titleTextStyle: WeddingTypography.lightTextTheme.headlineSmall,
      iconTheme: const IconThemeData(color: WeddingColors.darkGray),
    ),

    // 按钮主题
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        shadowColor: WeddingColors.shadowMedium,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: WeddingColors.primaryPink,
        foregroundColor: WeddingColors.pureWhite,
        textStyle: WeddingTypography.lightTextTheme.labelLarge,
      ),
    ),

    // 输入框主题
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: WeddingColors.pureWhite,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.mediumGray, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.mediumGray, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.primaryPink, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.error, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: WeddingTypography.lightTextTheme.bodyMedium?.copyWith(
        color: WeddingColors.mediumGray,
      ),
    ),

    // 卡片主题
    cardTheme: CardThemeData(
      elevation: 4,
      shadowColor: WeddingColors.shadowLight,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: WeddingColors.pureWhite,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    ),

    // 浮动操作按钮主题
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: WeddingColors.accentGold,
      foregroundColor: WeddingColors.pureWhite,
      elevation: 6,
      shape: CircleBorder(),
    ),
  );

  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    colorScheme: WeddingColors.darkColorScheme,
    textTheme: WeddingTypography.darkTextTheme,
    scaffoldBackgroundColor: WeddingColors.darkBackground,

    // AppBar主题
    appBarTheme: AppBarTheme(
      centerTitle: true,
      elevation: 0,
      backgroundColor: WeddingColors.darkSurface,
      surfaceTintColor: Colors.transparent,
      shadowColor: WeddingColors.shadowDark,
      titleTextStyle: WeddingTypography.darkTextTheme.headlineSmall,
      iconTheme: const IconThemeData(color: WeddingColors.pureWhite),
    ),

    // 按钮主题
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 2,
        shadowColor: WeddingColors.shadowDark,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: WeddingColors.primaryPinkLight,
        foregroundColor: WeddingColors.darkBackground,
        textStyle: WeddingTypography.darkTextTheme.labelLarge,
      ),
    ),

    // 输入框主题
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: WeddingColors.darkCard,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.darkGray, width: 1),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.darkGray, width: 1),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.primaryPinkLight, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: const BorderSide(color: WeddingColors.error, width: 1),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: WeddingTypography.darkTextTheme.bodyMedium?.copyWith(
        color: WeddingColors.mediumGray,
      ),
    ),

    // 卡片主题
    cardTheme: CardThemeData(
      elevation: 4,
      shadowColor: WeddingColors.shadowDark,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: WeddingColors.darkCard,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    ),

    // 浮动操作按钮主题
    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      backgroundColor: WeddingColors.accentGoldLight,
      foregroundColor: WeddingColors.darkBackground,
      elevation: 6,
      shape: CircleBorder(),
    ),
  );

  /// 切换主题模式
  static void toggleThemeMode(WidgetRef ref) {
    final currentMode = ref.read(appProvider).themeMode;
    final newMode = currentMode == ThemeMode.light
        ? ThemeMode.dark
        : ThemeMode.light;
    ref.read(appProvider.notifier).setThemeMode(newMode);
  }

  /// 设置主题模式
  static void setThemeMode(WidgetRef ref, ThemeMode themeMode) {
    ref.read(appProvider.notifier).setThemeMode(themeMode);
  }
}
