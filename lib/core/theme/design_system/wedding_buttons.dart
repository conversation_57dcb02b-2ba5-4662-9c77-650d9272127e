import 'package:flutter/material.dart';
import '../wedding_colors.dart';
import '../wedding_typography.dart';

/// 婚礼主题按钮组件
class WeddingButtons {
  
  /// 主要按钮 - 粉色渐变
  static Widget primary({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
    bool isEnabled = true,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) {
    return Container(
      width: width,
      height: height ?? 56,
      decoration: BoxDecoration(
        gradient: isEnabled ? WeddingColors.pinkGradient : null,
        color: isEnabled ? null : WeddingColors.mediumGray,
        borderRadius: BorderRadius.circular(16),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: WeddingColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled && !isLoading ? onPressed : null,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Center(
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(WeddingColors.pureWhite),
                      ),
                    )
                  : Text(
                      text,
                      style: WeddingTypography.lightTextTheme.labelLarge?.copyWith(
                        color: WeddingColors.pureWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 次要按钮 - 金色渐变
  static Widget secondary({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
    bool isEnabled = true,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) {
    return Container(
      width: width,
      height: height ?? 56,
      decoration: BoxDecoration(
        gradient: isEnabled ? WeddingColors.goldGradient : null,
        color: isEnabled ? null : WeddingColors.mediumGray,
        borderRadius: BorderRadius.circular(16),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: WeddingColors.shadowMedium,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled && !isLoading ? onPressed : null,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Center(
              child: isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(WeddingColors.pureWhite),
                      ),
                    )
                  : Text(
                      text,
                      style: WeddingTypography.lightTextTheme.labelLarge?.copyWith(
                        color: WeddingColors.pureWhite,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 轮廓按钮 - 透明背景带边框
  static Widget outline({
    required String text,
    required VoidCallback onPressed,
    bool isLoading = false,
    bool isEnabled = true,
    Color? borderColor,
    Color? textColor,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
  }) {
    final effectiveBorderColor = borderColor ?? WeddingColors.primaryPink;
    final effectiveTextColor = textColor ?? WeddingColors.primaryPink;
    
    return Container(
      width: width,
      height: height ?? 56,
      decoration: BoxDecoration(
        border: Border.all(
          color: isEnabled ? effectiveBorderColor : WeddingColors.mediumGray,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled && !isLoading ? onPressed : null,
          borderRadius: BorderRadius.circular(14),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Center(
              child: isLoading
                  ? SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(effectiveTextColor),
                      ),
                    )
                  : Text(
                      text,
                      style: WeddingTypography.lightTextTheme.labelLarge?.copyWith(
                        color: isEnabled ? effectiveTextColor : WeddingColors.mediumGray,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 文本按钮 - 纯文本
  static Widget text({
    required String text,
    required VoidCallback onPressed,
    bool isEnabled = true,
    Color? textColor,
    EdgeInsetsGeometry? padding,
  }) {
    final effectiveTextColor = textColor ?? WeddingColors.primaryPink;
    
    return TextButton(
      onPressed: isEnabled ? onPressed : null,
      style: TextButton.styleFrom(
        padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      child: Text(
        text,
        style: WeddingTypography.lightTextTheme.labelLarge?.copyWith(
          color: isEnabled ? effectiveTextColor : WeddingColors.mediumGray,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  /// 图标按钮
  static Widget icon({
    required IconData icon,
    required VoidCallback onPressed,
    bool isEnabled = true,
    Color? backgroundColor,
    Color? iconColor,
    double? size,
  }) {
    final effectiveBackgroundColor = backgroundColor ?? WeddingColors.primaryPinkLight;
    final effectiveIconColor = iconColor ?? WeddingColors.primaryPink;
    final effectiveSize = size ?? 48.0;
    
    return Container(
      width: effectiveSize,
      height: effectiveSize,
      decoration: BoxDecoration(
        color: isEnabled ? effectiveBackgroundColor : WeddingColors.lightGray,
        borderRadius: BorderRadius.circular(effectiveSize / 2),
        boxShadow: isEnabled ? [
          BoxShadow(
            color: WeddingColors.shadowLight,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onPressed : null,
          borderRadius: BorderRadius.circular(effectiveSize / 2),
          child: Icon(
            icon,
            color: isEnabled ? effectiveIconColor : WeddingColors.mediumGray,
            size: effectiveSize * 0.5,
          ),
        ),
      ),
    );
  }
}
