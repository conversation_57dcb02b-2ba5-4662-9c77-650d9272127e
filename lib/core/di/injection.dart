import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../network/dio_client.dart';
import '../network/network_info.dart';
import '../storage/local_storage.dart';
import '../storage/local_storage_impl.dart';
import '../utils/log_service.dart';
import '../services/permission_service.dart';
// import '../services/firebase_auth_service.dart';  // 暂时注释

// Auth imports
import '../../features/auth/data/datasources/auth_local_datasource.dart';
import '../../features/auth/data/datasources/auth_local_datasource_impl.dart';

import '../../features/auth/data/repositories/auth_repository_impl.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../../features/auth/domain/usecases/login_usecase.dart';
import '../../features/auth/domain/usecases/register_usecase.dart';
import '../../features/auth/domain/usecases/logout_usecase.dart';

// AI Generation imports
import '../../features/ai_generation/data/datasources/ai_generation_remote_datasource.dart';
import '../../features/ai_generation/data/datasources/image_upload_remote_datasource.dart';
import '../../features/ai_generation/data/repositories/ai_generation_repository_impl.dart';
import '../../features/ai_generation/domain/repositories/ai_generation_repository.dart';
// import '../../features/ai_generation/domain/repositories/image_upload_repository.dart';
import '../../features/ai_generation/domain/usecases/generate_wedding_photo_usecase.dart';
import '../../features/ai_generation/domain/usecases/get_style_templates_usecase.dart';
// import '../../features/ai_generation/domain/usecases/upload_images_usecase.dart';

final getIt = GetIt.instance;

/// 初始化依赖注入
Future<void> initializeDependencies() async {
  // 核心服务
  await _initializeCoreDependencies();

  // 功能模块
  _initializeFeatureDependencies();
}

/// 初始化核心依赖
Future<void> _initializeCoreDependencies() async {
  // SharedPreferences
  final sharedPreferences = await SharedPreferences.getInstance();
  getIt.registerSingleton<SharedPreferences>(sharedPreferences);

  // 网络连接检查
  getIt.registerLazySingleton<Connectivity>(() => Connectivity());
  getIt.registerLazySingleton<NetworkInfo>(() => NetworkInfoImpl(getIt()));

  // 网络客户端
  getIt.registerLazySingleton<DioClient>(() => DioClient());

  // 本地存储
  getIt.registerLazySingleton<LocalStorage>(() => LocalStorageImpl(getIt()));

  // 日志服务
  getIt.registerLazySingleton<LogService>(() => LogService());

  // 权限服务
  getIt.registerLazySingleton<PermissionService>(() => PermissionService());

  // Firebase认证服务 - 暂时注释
  // getIt.registerLazySingleton<FirebaseAuthService>(() => FirebaseAuthService(
  //   logger: getIt(),
  // ));
}

/// 初始化功能模块依赖
void _initializeFeatureDependencies() {
  // 认证模块
  _initializeAuthDependencies();

  // 首页模块
  _initializeHomeDependencies();

  // 设置模块
  _initializeSettingsDependencies();

  // AI生成模块
  _initializeAiGenerationDependencies();

  // 其他功能模块
}

/// 认证模块依赖
void _initializeAuthDependencies() {
  // 数据源
  getIt.registerLazySingleton<AuthLocalDataSource>(() => AuthLocalDataSourceImpl(getIt()));

  // 仓库
  getIt.registerLazySingleton<AuthRepository>(() => AuthRepositoryImpl(getIt(), getIt()));

  // 用例
  getIt.registerFactory<LoginUseCase>(() => LoginUseCase(getIt()));
  getIt.registerFactory<RegisterUseCase>(() => RegisterUseCase(getIt()));
  getIt.registerFactory<LogoutUseCase>(() => LogoutUseCase(getIt()));
}

/// 首页模块依赖
void _initializeHomeDependencies() {
  // 暂时注释，等待实现具体的类
  // 数据源
  // getIt.registerLazySingleton<HomeRemoteDataSource>(() => HomeRemoteDataSourceImpl(getIt()));

  // 仓库
  // getIt.registerLazySingleton<HomeRepository>(() => HomeRepositoryImpl(getIt()));

  // 用例
  // getIt.registerFactory<GetHomeDataUseCase>(() => GetHomeDataUseCase(getIt()));
}

/// 设置模块依赖
void _initializeSettingsDependencies() {
  // 暂时注释，等待实现具体的类
  // 仓库
  // getIt.registerLazySingleton<SettingsRepository>(() => SettingsRepositoryImpl(getIt()));

  // 用例
  // getIt.registerFactory<UpdateThemeUseCase>(() => UpdateThemeUseCase(getIt()));
  // getIt.registerFactory<UpdateLanguageUseCase>(() => UpdateLanguageUseCase(getIt()));
}

/// AI生成模块依赖
void _initializeAiGenerationDependencies() {
  // 数据源
  getIt.registerLazySingleton<AiGenerationRemoteDataSource>(
    () => AiGenerationRemoteDataSourceImpl(getIt(), getIt()),
  );
  getIt.registerLazySingleton<ImageUploadRemoteDataSource>(
    () => ImageUploadRemoteDataSourceImpl(getIt(), getIt()),
  );

  // 仓库
  getIt.registerLazySingleton<AiGenerationRepository>(
    () => AiGenerationRepositoryImpl(getIt(), getIt(), getIt()),
  );
  // TODO: 实现ImageUploadRepository
  // getIt.registerLazySingleton<ImageUploadRepository>(
  //   () => ImageUploadRepositoryImpl(getIt(), getIt(), getIt()),
  // );

  // 用例
  getIt.registerFactory<GetStyleTemplatesUseCase>(
    () => GetStyleTemplatesUseCase(getIt(), getIt()),
  );
  getIt.registerFactory<GenerateWeddingPhotoUseCase>(
    () => GenerateWeddingPhotoUseCase(getIt(), getIt()),
  );
  // TODO: 实现UploadImagesUseCase
  // getIt.registerFactory<UploadImagesUseCase>(
  //   () => UploadImagesUseCase(getIt(), getIt()),
  // );
}