import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';

import '../../../../core/services/ai_generation_service.dart';
import '../../../../core/services/image_save_service.dart';

/// 婚纱照生成结果页面
class GenerationResultScreen extends StatefulWidget {
  final GenerationResult result;

  const GenerationResultScreen({
    super.key,
    required this.result,
  });

  @override
  State<GenerationResultScreen> createState() => _GenerationResultScreenState();
}

class _GenerationResultScreenState extends State<GenerationResultScreen> {
  bool _isSaving = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('生成结果'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareResult(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.result.success) ...[
              // 生成的婚纱照
              _buildGeneratedImage(),
              
              SizedBox(height: 24.h),
              
              // AI分析结果
              _buildAnalysisSection(),
              
              SizedBox(height: 24.h),
              
              // 操作按钮
              _buildActionButtons(context),
            ] else ...[
              // 错误状态
              _buildErrorState(context),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建生成的图片展示
  Widget _buildGeneratedImage() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: CachedNetworkImage(
          imageUrl: widget.result.generatedImageUrl,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            height: 400.h,
            color: Colors.grey.shade100,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Colors.pink.shade400,
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    '正在加载生成的婚纱照...',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            height: 400.h,
            color: Colors.grey.shade100,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 48.sp,
                    color: Colors.red.shade400,
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    '图片加载失败',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.red.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建AI分析结果区域
  Widget _buildAnalysisSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.purple.shade50,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
          color: Colors.purple.shade200,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: Colors.purple.shade600,
                size: 24.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'AI 分析结果',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.purple.shade800,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              widget.result.analysis,
              style: TextStyle(
                fontSize: 14.sp,
                height: 1.6,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          SizedBox(height: 12.h),
          GestureDetector(
            onTap: () => _copyAnalysis(),
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 12.w,
                vertical: 8.h,
              ),
              decoration: BoxDecoration(
                color: Colors.purple.shade100,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.copy,
                    size: 16.sp,
                    color: Colors.purple.shade600,
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    '复制分析结果',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.purple.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // 重新生成按钮
        Container(
          width: double.infinity,
          height: 50.h,
          child: ElevatedButton.icon(
            onPressed: () => _regeneratePhoto(context),
            icon: Icon(Icons.refresh, size: 20.sp),
            label: Text(
              '重新生成',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple.shade400,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
              elevation: 2,
            ),
          ),
        ),
        
        SizedBox(height: 12.h),
        
        // 保存图片按钮
        Container(
          width: double.infinity,
          height: 50.h,
          child: OutlinedButton.icon(
            onPressed: _isSaving ? null : () => _saveImage(context),
            icon: _isSaving
                ? SizedBox(
                    width: 20.sp,
                    height: 20.sp,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.purple.shade600),
                    ),
                  )
                : Icon(Icons.download, size: 20.sp),
            label: Text(
              _isSaving ? '保存中...' : '保存到相册',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: _isSaving ? Colors.grey.shade400 : Colors.purple.shade600,
              side: BorderSide(color: _isSaving ? Colors.grey.shade300 : Colors.purple.shade300),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
              ),
            ),
          ),
        ),
        
        SizedBox(height: 12.h),
        
        // 返回按钮
        Container(
          width: double.infinity,
          height: 50.h,
          child: TextButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.home, size: 20.sp),
            label: Text(
              '返回首页',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            style: TextButton.styleFrom(
              foregroundColor: Colors.grey.shade600,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(BuildContext context) {
    return Center(
      child: Container(
        padding: EdgeInsets.all(32.w),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 80.sp,
              color: Colors.red.shade400,
            ),
            SizedBox(height: 24.h),
            Text(
              '生成失败',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade600,
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              widget.result.message,
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 32.h),
            Container(
              width: double.infinity,
              height: 50.h,
              child: ElevatedButton.icon(
                onPressed: () => Navigator.pop(context),
                icon: Icon(Icons.arrow_back, size: 20.sp),
                label: Text(
                  '返回重试',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade400,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 复制分析结果
  void _copyAnalysis() {
    Clipboard.setData(ClipboardData(text: widget.result.analysis));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.copy, color: Colors.white),
            const SizedBox(width: 8),
            const Text('分析结果已复制到剪贴板'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 分享结果
  void _shareResult(BuildContext context) {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// 重新生成照片
  void _regeneratePhoto(BuildContext context) {
    // TODO: 实现重新生成功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('重新生成功能开发中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// 直接保存图片到相册
  Future<void> _saveImage(BuildContext context) async {
    if (!widget.result.success || widget.result.generatedImageUrl.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('没有可保存的图片'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final fileName = 'hera_wedding_photo_${DateTime.now().millisecondsSinceEpoch}';
      final success = await ImageSaveService.saveNetworkImageToGallery(
        widget.result.generatedImageUrl,
        context: context,
        fileName: fileName,
      );

      if (success) {
        // 保存成功的提示已在 ImageSaveService 中显示
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('保存失败：$e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }


} 