import 'package:dartz/dartz.dart';

import '../../../../core/error/network_exceptions.dart';
import '../entities/user.dart';

/// 认证仓库接口
abstract class AuthRepository {
  /// 邮箱密码登录
  Future<Either<NetworkExceptions, User>> login(String email, String password);

  /// 邮箱密码注册
  Future<Either<NetworkExceptions, User>> register(String email, String password, String? name);

  /// Google登录
  Future<Either<NetworkExceptions, User>> signInWithGoogle();

  /// Apple登录
  Future<Either<NetworkExceptions, User>> signInWithApple();

  /// 登出
  Future<Either<NetworkExceptions, void>> logout();

  /// 获取当前用户
  Future<User?> getCurrentUser();

  /// 用户状态变化流
  Stream<User?> get userChanges;

  /// 检查是否已登录
  Future<bool> isLoggedIn();

  /// 发送密码重置邮件
  Future<Either<NetworkExceptions, void>> sendPasswordResetEmail(String email);

  /// 发送邮箱验证
  Future<Either<NetworkExceptions, void>> sendEmailVerification();

  /// 删除账户
  Future<Either<NetworkExceptions, void>> deleteAccount();
}
