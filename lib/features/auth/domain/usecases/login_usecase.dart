import 'package:dartz/dartz.dart';

import '../../../../core/error/network_exceptions.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// 登录用例
class LoginUseCase {
  final AuthRepository _repository;

  LoginUseCase(this._repository);

  /// 执行邮箱密码登录
  Future<Either<NetworkExceptions, User>> call(String email, String password) async {
    // 验证输入
    if (email.isEmpty) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (password.isEmpty) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (!_isValidEmail(email)) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (password.length < 6) {
      return const Left(NetworkExceptions.badRequest());
    }

    // 执行登录
    return await _repository.login(email, password);
  }

  /// 执行Google登录
  Future<Either<NetworkExceptions, User>> signInWithGoogle() async {
    return await _repository.signInWithGoogle();
  }

  /// 执行Apple登录
  Future<Either<NetworkExceptions, User>> signInWithApple() async {
    return await _repository.signInWithApple();
  }
  
  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }
}
