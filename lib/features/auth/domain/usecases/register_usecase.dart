import 'package:dartz/dartz.dart';

import '../../../../core/error/network_exceptions.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// 注册用例
class RegisterUseCase {
  final AuthRepository _repository;

  RegisterUseCase(this._repository);

  /// 执行注册
  Future<Either<NetworkExceptions, User>> call(String email, String password, String? name) async {
    // 验证输入
    if (email.isEmpty) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (password.isEmpty) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (!_isValidEmail(email)) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (password.length < 6) {
      return const Left(NetworkExceptions.badRequest());
    }

    if (!_isValidPassword(password)) {
      return const Left(NetworkExceptions.badRequest());
    }

    // 执行注册
    return await _repository.register(email, password, name);
  }
  
  /// 验证邮箱格式
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        .hasMatch(email);
  }
  
  /// 验证密码强度
  bool _isValidPassword(String password) {
    return RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d!@#$%^&*()_+]{6,}$')
        .hasMatch(password);
  }
}
