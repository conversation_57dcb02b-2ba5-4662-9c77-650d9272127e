import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../../../core/di/injection.dart';
import '../../../../core/error/network_exceptions.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/login_usecase.dart';
import '../../domain/usecases/register_usecase.dart';
import '../../domain/usecases/logout_usecase.dart';

part 'auth_provider.freezed.dart';

/// 认证状态
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    @Default(false) bool isLoading,
    @Default(false) bool isAuthenticated,
    User? user,
    String? error,
    String? token,
  }) = _AuthState;
}

/// 认证状态管理器
class AuthNotifier extends StateNotifier<AuthState> {
  final LoginUseCase _loginUseCase;
  final RegisterUseCase _registerUseCase;
  final LogoutUseCase _logoutUseCase;

  AuthNotifier(this._loginUseCase, this._registerUseCase, this._logoutUseCase)
      : super(const AuthState());

  /// 登录
  Future<void> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _loginUseCase(email, password);

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          error: _getErrorMessage(failure),
        );
      },
      (user) {
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: user,
          token: 'firebase_token', // Firebase自动管理token
        );
      },
    );
  }

  /// 注册
  Future<void> register(String email, String password, [String? name]) async {
    state = state.copyWith(isLoading: true, error: null);

    final result = await _registerUseCase(email, password, name);

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          error: _getErrorMessage(failure),
        );
      },
      (user) {
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          user: user,
          token: 'firebase_token', // Firebase自动管理token
        );
      },
    );
  }

  /// 登出
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);

    final result = await _logoutUseCase();

    result.fold(
      (failure) {
        state = state.copyWith(
          isLoading: false,
          error: '登出失败：${_getErrorMessage(failure)}',
        );
      },
      (_) {
        state = const AuthState();
      },
    );
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 从本地存储恢复状态
  Future<void> restoreAuthState() async {
    // TODO: 从本地存储读取token和用户信息
    // 这里暂时模拟
    await Future.delayed(const Duration(milliseconds: 100));

    // 如果有有效的token，设置为已认证状态
    // final token = await _localStorage.getString('auth_token');
    // if (token != null && token.isNotEmpty) {
    //   state = state.copyWith(
    //     isAuthenticated: true,
    //     token: token,
    //     email: await _localStorage.getString('user_email') ?? '',
    //   );
    // }
  }

  /// 获取错误消息
  String _getErrorMessage(NetworkExceptions exception) {
    return exception.when(
      requestCancelled: () => '请求已取消',
      unauthorizedRequest: () => '邮箱或密码错误',
      badRequest: () => '请求参数错误',
      forbidden: () => '账户已被禁用',
      notFound: (reason) => '用户不存在',
      methodNotAllowed: () => '请求方法不被允许',
      requestTimeout: () => '请求超时，请重试',
      internalServerError: () => '服务器内部错误',
      serviceUnavailable: () => '服务暂不可用',
      noInternetConnection: () => '网络连接不可用，请检查网络设置',
      formatException: () => '数据格式错误',
      unableToProcess: () => '无法处理请求',
      defaultError: (error) => error,
      unexpectedError: () => '发生意外错误',
    );
  }
}

/// 认证状态提供者
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final loginUseCase = getIt<LoginUseCase>();
  final registerUseCase = getIt<RegisterUseCase>();
  final logoutUseCase = getIt<LogoutUseCase>();

  return AuthNotifier(loginUseCase, registerUseCase, logoutUseCase);
});

/// 是否已认证的计算属性
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

/// 当前用户的计算属性
final currentUserProvider = Provider<User?>((ref) {
  return ref.watch(authProvider).user;
});

/// 当前用户邮箱的计算属性
final currentUserEmailProvider = Provider<String>((ref) {
  final user = ref.watch(authProvider).user;
  return user?.email ?? '';
});
