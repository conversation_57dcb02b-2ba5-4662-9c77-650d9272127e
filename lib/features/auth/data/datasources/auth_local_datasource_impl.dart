import 'dart:convert';

import '../../../../core/storage/local_storage.dart';
import '../../../../shared/constants/app_constants.dart';
import '../../domain/entities/user.dart';
import 'auth_local_datasource.dart';

/// 认证本地数据源实现
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final LocalStorage _localStorage;

  AuthLocalDataSourceImpl(this._localStorage);

  @override
  Future<void> saveUserData(User user) async {
    final userJson = {
      'id': user.id,
      'email': user.email,
      'name': user.name,
      'avatar': user.avatar,
      'phoneNumber': user.phoneNumber,
      'isEmailVerified': user.isEmailVerified,
      'isPremiumUser': user.isPremiumUser,
      'dailyGenerationCount': user.dailyGenerationCount,
      'monthlyGenerationCount': user.monthlyGenerationCount,
      'premiumExpiryDate': user.premiumExpiryDate?.toIso8601String(),
      'createdAt': user.createdAt?.toIso8601String(),
      'updatedAt': user.updatedAt?.toIso8601String(),
      'lastLoginAt': user.lastLoginAt?.toIso8601String(),
    };

    await _localStorage.setString(
      StorageKeys.userInfo,
      jsonEncode(userJson),
    );
  }

  @override
  Future<User?> getUserData() async {
    final userJsonString = await _localStorage.getString(StorageKeys.userInfo);
    if (userJsonString == null) return null;

    try {
      final userJson = jsonDecode(userJsonString) as Map<String, dynamic>;

      return User(
        id: userJson['id'] as String,
        email: userJson['email'] as String,
        name: userJson['name'] as String?,
        avatar: userJson['avatar'] as String?,
        phoneNumber: userJson['phoneNumber'] as String?,
        isEmailVerified: userJson['isEmailVerified'] as bool?,
        isPremiumUser: userJson['isPremiumUser'] as bool?,
        dailyGenerationCount: userJson['dailyGenerationCount'] as int?,
        monthlyGenerationCount: userJson['monthlyGenerationCount'] as int?,
        premiumExpiryDate: userJson['premiumExpiryDate'] != null
            ? DateTime.parse(userJson['premiumExpiryDate'] as String)
            : null,
        createdAt: userJson['createdAt'] != null
            ? DateTime.parse(userJson['createdAt'] as String)
            : null,
        updatedAt: userJson['updatedAt'] != null
            ? DateTime.parse(userJson['updatedAt'] as String)
            : null,
        lastLoginAt: userJson['lastLoginAt'] != null
            ? DateTime.parse(userJson['lastLoginAt'] as String)
            : null,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    await Future.wait([
      _localStorage.remove(StorageKeys.userInfo),
      _localStorage.remove('user_preferences'),
    ]);
  }

  @override
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    await _localStorage.setString(
      'user_preferences',
      jsonEncode(preferences),
    );
  }

  @override
  Future<Map<String, dynamic>?> getUserPreferences() async {
    final preferencesString = await _localStorage.getString('user_preferences');
    if (preferencesString == null) return null;

    try {
      return jsonDecode(preferencesString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }
}
