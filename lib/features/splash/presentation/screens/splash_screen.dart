import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../core/router/app_router.dart';
import '../../../../../core/theme/wedding_colors.dart';
import '../../../../../core/services/first_launch_service.dart';
import '../../../../../core/di/injection.dart';
import '../../../../../shared/constants/app_constants.dart';

@RoutePage()
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    // 延迟导航，以显示启动页面
    await Future.delayed(AppDurations.splash);

    // 获取首次启动服务
    final firstLaunchService = getIt<FirstLaunchService>();

    // 根据首次启动状态决定导航目标
    if (mounted) {
      if (firstLaunchService.isFirstLaunch || !firstLaunchService.isOnboardingCompleted) {
        // 首次启动或引导未完成，导航到引导页面
        context.router.replace(const OnboardingRoute());
      } else {
        // 已完成引导，导航到主页面
        context.router.replace(const MainRoute());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: WeddingColors.weddingGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部装饰
              Expanded(
                flex: 2,
                child: Container(
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 婚礼图标/Logo
                      Container(
                        width: 120.w,
                        height: 120.w,
                        decoration: BoxDecoration(
                          color: WeddingColors.pureWhite.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(60.w),
                          boxShadow: [
                            BoxShadow(
                              color: WeddingColors.shadowMedium,
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.favorite,
                          size: 60.sp,
                          color: WeddingColors.primaryPink,
                        ),
                      ),
                      SizedBox(height: 32.h),
                      // 应用名称 - 使用脚本字体
                      Text(
                        'Hera',
                        style: TextStyle(
                          fontFamily: 'Dancing Script',
                          fontSize: 48.sp,
                          fontWeight: FontWeight.w400,
                          color: WeddingColors.pureWhite,
                          shadows: [
                            Shadow(
                              color: WeddingColors.shadowMedium,
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 8.h),
                      // 副标题
                      Text(
                        'AI婚纱照生成器',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w500,
                          color: WeddingColors.pureWhite.withOpacity(0.9),
                          letterSpacing: 2,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // 中间装饰元素
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 装饰性的心形图案
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        _buildHeartIcon(size: 12.sp),
                        SizedBox(width: 16.w),
                        _buildHeartIcon(size: 16.sp),
                        SizedBox(width: 16.w),
                        _buildHeartIcon(size: 12.sp),
                      ],
                    ),
                    SizedBox(height: 24.h),
                    // 标语
                    Text(
                      '用AI创造完美的婚纱照',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: WeddingColors.pureWhite.withOpacity(0.8),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),

              // 底部加载区域
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 自定义加载指示器
                    SizedBox(
                      width: 40.w,
                      height: 40.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          WeddingColors.pureWhite.withOpacity(0.8),
                        ),
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Text(
                      '正在初始化...',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: WeddingColors.pureWhite.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ),

              // 底部版权信息
              Padding(
                padding: EdgeInsets.only(bottom: 24.h),
                child: Text(
                  '© 2024 Hera AI',
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: WeddingColors.pureWhite.withOpacity(0.6),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeartIcon({required double size}) {
    return Icon(
      Icons.favorite,
      size: size,
      color: WeddingColors.pureWhite.withOpacity(0.6),
    );
  }
} 