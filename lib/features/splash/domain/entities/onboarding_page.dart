import 'package:flutter/material.dart';

/// 引导页面实体
class OnboardingPage {
  final String title;
  final String description;
  final IconData icon;
  final Color? iconColor;
  final String? imagePath;
  final List<String>? features;

  const OnboardingPage({
    required this.title,
    required this.description,
    required this.icon,
    this.iconColor,
    this.imagePath,
    this.features,
  });
}

/// 预定义的引导页面数据
class OnboardingData {
  static const List<OnboardingPage> pages = [
    OnboardingPage(
      title: '欢迎来到Hera',
      description: '用AI技术创造完美的婚纱照，让每一刻都成为永恒的回忆',
      icon: Icons.favorite,
      features: [
        '专业级AI图片生成',
        '多种婚纱照风格',
        '简单易用的操作',
      ],
    ),
    OnboardingPage(
      title: '上传您的照片',
      description: '只需上传您的照片，我们的AI将为您生成各种风格的婚纱照',
      icon: Icons.photo_camera,
      features: [
        '支持多种图片格式',
        '智能人脸检测',
        '照片质量优化',
      ],
    ),
    OnboardingPage(
      title: '选择您喜欢的风格',
      description: '从中式传统到西式经典，从复古怀旧到现代简约，总有一款适合您',
      icon: Icons.palette,
      features: [
        '中式传统风格',
        '西式经典风格',
        '复古与现代风格',
      ],
    ),
    OnboardingPage(
      title: '生成完美婚纱照',
      description: 'AI将在几分钟内为您生成高质量的婚纱照，让您的爱情故事更加完美',
      icon: Icons.auto_awesome,
      features: [
        '高质量图片输出',
        '快速生成速度',
        '多种分辨率选择',
      ],
    ),
  ];
}
