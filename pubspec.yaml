name: hera
description: "<PERSON><PERSON> AI婚纱照生成应用 - 用AI技术生成专属婚纱照"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.0
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  shimmer: ^3.0.0

  # 状态管理
  flutter_riverpod: ^2.5.1
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1

  # 路由管理
  auto_route: ^7.8.5

  # 网络请求
  dio: ^5.4.2
  connectivity_plus: ^5.0.2

  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # 依赖注入
  get_it: ^7.6.7

  # 工具类
  intl: ^0.20.0
  logger: ^2.0.2+1
  path_provider: ^2.1.2
  device_info_plus: ^9.1.2
  package_info_plus: ^5.0.1
  url_launcher: ^6.2.5

  # AI图像生成相关
  image_picker: ^1.0.7
  image: ^4.1.7
  permission_handler: ^11.4.0
  http_parser: ^4.0.2
  
  # 图片保存到相册
  image_gallery_saver: ^2.0.3

  # 函数式编程
  dartz: ^0.10.1

  # Firebase相关 - 暂时注释，解决编译问题
  # firebase_core: ^2.27.0
  # firebase_auth: ^4.17.8
  # google_sign_in: ^6.2.0  # 暂时注释，解决版本冲突
  # sign_in_with_apple: ^6.1.0  # 暂时注释，解决版本冲突

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # 代码生成
  build_runner: ^2.4.8
  freezed: ^2.4.7
  json_serializable: ^6.7.1
  auto_route_generator: ^7.3.2
  hive_generator: ^2.0.1
  flutter_gen_runner: ^5.4.0

  # 测试相关
  mockito: ^5.4.4

  # 图标生成工具
  flutter_launcher_icons: ^0.13.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  # assets 配置
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/translations/

# 应用图标配置
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true # 移除iOS图标的alpha通道，符合App Store要求
  web:
    generate: true
    image_path: "assets/images/app_icon.png"
    background_color: "#FFFFFF"
    theme_color: "#2196F3"
  windows:
    generate: true
    image_path: "assets/images/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/app_icon.png"
