# 保存到相册功能测试指南

## 功能概述

本文档描述了如何测试"保存到相册"功能，该功能允许用户将AI生成的婚纱照直接保存到设备的照片库中。

## 改进内容

### 1. 权限管理优化
- ✅ 集成了统一的权限管理服务 (`PermissionService`)
- ✅ 支持Android和iOS的相册权限请求
- ✅ 处理权限被永久拒绝的情况
- ✅ 提供友好的权限说明对话框

### 2. 用户界面改进
- ✅ 保存按钮显示加载状态
- ✅ 动态显示权限设置按钮（仅在需要时显示）
- ✅ 提供直接跳转到系统设置的功能
- ✅ 优化错误提示和成功反馈

### 3. 权限处理流程
- ✅ 自动检测权限状态
- ✅ 智能权限请求（避免重复请求）
- ✅ 权限被拒绝时的引导流程
- ✅ 支持从设置返回后自动刷新状态

## 测试步骤

### 准备工作
1. 确保应用已安装在真机上（模拟器无法完全测试权限功能）
2. 确保设备有网络连接
3. 准备一些测试图片用于生成婚纱照

### 测试场景

#### 场景1：首次使用（权限未授权）
1. 打开应用，生成一张婚纱照
2. 点击"保存到相册"按钮
3. **预期结果**：
   - 显示权限请求对话框
   - 用户可以选择"好的，去授权"或"残忍拒绝"
   - 选择授权后，系统弹出权限请求
   - 授权成功后，图片保存到相册

#### 场景2：权限被拒绝
1. 在场景1中选择"拒绝"权限
2. 再次点击"保存到相册"按钮
3. **预期结果**：
   - 显示"开启相册权限"按钮
   - 点击该按钮显示设置引导对话框
   - 可以直接跳转到系统设置

#### 场景3：权限被永久拒绝
1. 在系统设置中永久拒绝相册权限
2. 点击"保存到相册"按钮
3. **预期结果**：
   - 显示详细的设置引导对话框
   - 提供步骤说明
   - 可以直接跳转到系统设置

#### 场景4：权限已授权
1. 确保相册权限已授权
2. 点击"保存到相册"按钮
3. **预期结果**：
   - 显示"保存中..."状态
   - 图片成功保存到相册
   - 显示"图片已保存到相册"成功提示
   - 权限设置按钮不显示

#### 场景5：网络错误
1. 断开网络连接
2. 点击"保存到相册"按钮
3. **预期结果**：
   - 显示网络错误提示
   - 保存状态正确重置

### 验证要点

#### 权限检查
- [ ] 应用能正确检测当前权限状态
- [ ] 权限请求对话框内容清晰易懂
- [ ] 权限被拒绝时有合适的引导

#### 用户体验
- [ ] 保存按钮状态变化正确（正常→加载→完成）
- [ ] 权限设置按钮仅在需要时显示
- [ ] 错误提示信息准确且有帮助
- [ ] 成功提示及时显示

#### 功能完整性
- [ ] 图片能成功下载并保存
- [ ] 保存的图片质量良好
- [ ] 文件名包含时间戳，避免重复
- [ ] 支持各种图片格式

#### 兼容性
- [ ] Android 13+ 使用新的相册权限
- [ ] Android 12及以下兼容存储权限
- [ ] iOS 相册权限正常工作
- [ ] 不同设备和系统版本表现一致

## 常见问题排查

### 问题1：权限请求失败
**症状**：点击授权后仍然无法保存
**排查**：
1. 检查 `AndroidManifest.xml` 权限配置
2. 检查 `Info.plist` 权限描述
3. 查看控制台日志

### 问题2：图片保存失败
**症状**：权限正常但保存失败
**排查**：
1. 检查网络连接
2. 验证图片URL有效性
3. 检查设备存储空间

### 问题3：UI状态异常
**症状**：按钮状态不正确
**排查**：
1. 检查 `setState` 调用
2. 验证权限检查逻辑
3. 确认异步操作处理

## 技术实现要点

### 权限管理
- 使用 `PermissionService` 统一管理权限
- 支持权限状态检查和请求
- 处理各种权限状态（授权、拒绝、永久拒绝等）

### 图片保存
- 使用 `ImageSaveService` 处理图片下载和保存
- 支持网络图片和本地图片
- 提供详细的错误处理和用户反馈

### 用户界面
- 动态显示权限相关UI组件
- 提供清晰的操作引导
- 优化加载状态和错误提示

## 后续优化建议

1. **批量保存**：支持同时保存多张图片
2. **保存历史**：记录已保存的图片，避免重复
3. **自定义相册**：创建专门的应用相册
4. **保存进度**：显示下载和保存进度
5. **离线支持**：缓存图片支持离线保存
