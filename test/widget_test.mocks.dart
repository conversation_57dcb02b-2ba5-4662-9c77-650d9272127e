// Mocks generated by <PERSON>ckito 5.4.5 from annotations
// in hera/test/widget_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:dartz/dartz.dart' as _i2;
import 'package:hera/core/error/network_exceptions.dart' as _i6;
import 'package:hera/features/auth/domain/entities/user.dart' as _i5;
import 'package:hera/features/auth/domain/repositories/auth_repository.dart'
    as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeEither_0<L, R> extends _i1.SmartFake implements _i2.Either<L, R> {
  _FakeEither_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i3.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i5.User?> get userChanges => (super.noSuchMethod(
        Invocation.getter(#userChanges),
        returnValue: _i4.Stream<_i5.User?>.empty(),
      ) as _i4.Stream<_i5.User?>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>> login(
    String? email,
    String? password,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [
            email,
            password,
          ],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>.value(
                _FakeEither_0<_i6.NetworkExceptions, _i5.User>(
          this,
          Invocation.method(
            #login,
            [
              email,
              password,
            ],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>> register(
    String? email,
    String? password,
    String? name,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [
            email,
            password,
            name,
          ],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>.value(
                _FakeEither_0<_i6.NetworkExceptions, _i5.User>(
          this,
          Invocation.method(
            #register,
            [
              email,
              password,
              name,
            ],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>> signInWithGoogle() =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithGoogle,
          [],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>.value(
                _FakeEither_0<_i6.NetworkExceptions, _i5.User>(
          this,
          Invocation.method(
            #signInWithGoogle,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>> signInWithApple() =>
      (super.noSuchMethod(
        Invocation.method(
          #signInWithApple,
          [],
        ),
        returnValue:
            _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>.value(
                _FakeEither_0<_i6.NetworkExceptions, _i5.User>(
          this,
          Invocation.method(
            #signInWithApple,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, _i5.User>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, void>> logout() =>
      (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>.value(
            _FakeEither_0<_i6.NetworkExceptions, void>(
          this,
          Invocation.method(
            #logout,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>);

  @override
  _i4.Future<_i5.User?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i4.Future<_i5.User?>.value(),
      ) as _i4.Future<_i5.User?>);

  @override
  _i4.Future<bool> isLoggedIn() => (super.noSuchMethod(
        Invocation.method(
          #isLoggedIn,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, void>> sendPasswordResetEmail(
          String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendPasswordResetEmail,
          [email],
        ),
        returnValue: _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>.value(
            _FakeEither_0<_i6.NetworkExceptions, void>(
          this,
          Invocation.method(
            #sendPasswordResetEmail,
            [email],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, void>> sendEmailVerification() =>
      (super.noSuchMethod(
        Invocation.method(
          #sendEmailVerification,
          [],
        ),
        returnValue: _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>.value(
            _FakeEither_0<_i6.NetworkExceptions, void>(
          this,
          Invocation.method(
            #sendEmailVerification,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>);

  @override
  _i4.Future<_i2.Either<_i6.NetworkExceptions, void>> deleteAccount() =>
      (super.noSuchMethod(
        Invocation.method(
          #deleteAccount,
          [],
        ),
        returnValue: _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>.value(
            _FakeEither_0<_i6.NetworkExceptions, void>(
          this,
          Invocation.method(
            #deleteAccount,
            [],
          ),
        )),
      ) as _i4.Future<_i2.Either<_i6.NetworkExceptions, void>>);
}
