import 'package:flutter_test/flutter_test.dart';
import 'package:app_hera_flutter/features/auth/domain/usecases/login_usecase.dart';
import 'package:app_hera_flutter/features/auth/domain/repositories/auth_repository.dart';
import 'package:app_hera_flutter/features/auth/domain/entities/user.dart';
import 'package:app_hera_flutter/core/error/network_exceptions.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:dartz/dartz.dart';

import 'widget_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('LoginUseCase Tests', () {
    late LoginUseCase loginUseCase;
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      loginUseCase = LoginUseCase(mockAuthRepository);
    });

    test('should return User when login is successful', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const user = User(
        id: '1',
        email: email,
        name: 'Test User',
      );

      when(mockAuthRepository.login(email, password))
          .thenAnswer((_) async => Right(user));

      // Act
      final result = await loginUseCase(email, password);

      // Assert
      expect(result, equals(Right(user)));
      verify(mockAuthRepository.login(email, password)).called(1);
    });

    test('should return Left when email is empty', () async {
      // Arrange
      const email = '';
      const password = 'password123';

      // Act
      final result = await loginUseCase(email, password);

      // Assert
      expect(result.isLeft(), true);
    });

    test('should return Left when password is empty', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = '';

      // Act
      final result = await loginUseCase(email, password);

      // Assert
      expect(result.isLeft(), true);
    });

    test('should return Left when email format is invalid', () async {
      // Arrange
      const email = 'invalid-email';
      const password = 'password123';

      // Act
      final result = await loginUseCase(email, password);

      // Assert
      expect(result.isLeft(), true);
    });

    test('should return Left when password is too short', () async {
      // Arrange
      const email = '<EMAIL>';
      const password = '123';

      // Act
      final result = await loginUseCase(email, password);

      // Assert
      expect(result.isLeft(), true);
    });
  });
}
